# User Guidelines - 协同工作策略自动执行版

## 🚨 绝对优先执行规则

### 协同工作策略强制触发
**涉及现有代码库时 → 立即自动执行：**
```
@Sequential_thinking + @codebase-retrieval 协同分析
```

**触发条件：**
- 分析/修改/理解现有代码
- 询问项目结构/功能/实现
- 需要与现有代码集成

**执行流程：**
1. 启动分步骤思考工具分析任务
2. 思考过程中调用ACE验证代码上下文  
3. 协同循环直至完成理解

## 🔧 工具使用优先级

### 强制优先级顺序
1. **协同策略** (涉及代码库时): `@Sequential_thinking + @codebase-retrieval`
2. **文件系统**: `@desktop-commander` > 基础工具
3. **文件编辑**: `@str-replace-editor` (修改文件内容时)
4. **技术调研**: `@Context_7` → `Web Tools` (开发前必须)
5. **反馈机制**: `@mcp-feedback-enhanced` (每次回复必须)

### 自动检测关键词
- "分析代码"、"查看代码"、"理解代码"
- "修改"、"编辑"、"重构"、"优化"  
- "项目结构"、"文件结构"、"代码架构"
- "函数实现"、"类定义"、"方法逻辑"
- 具体文件名或代码符号

## 📋 执行检查清单

### 任务开始前自动检查
```
□ 是否涉及现有代码库？
  ├─ 是 → 立即执行协同工作策略
  └─ 否 → 继续正常流程

□ 是否需要文件系统操作？
  └─ 是 → 优先使用 @desktop-commander

□ 是否需要技术调研？
  └─ 是 → 先调用 @Context_7

□ 每次回复结束
  └─ 必须调用 @mcp-feedback-enhanced
```

## 🎯 核心执行原则

- **自动性**: AI主动检测并执行，无需用户提醒
- **强制性**: 绝对强制要求，不允许跳过
- **完整性**: 必须完成完整的协同分析流程
- **智能性**: 避免不必要的工具调用

## 💡 快速参考

### 常见场景自动执行
- **代码分析** → `@Sequential_thinking + @codebase-retrieval`
- **文件操作** → `@desktop-commander`
- **代码编辑** → `@str-replace-editor`
- **技术查询** → `@Context_7`
- **获取反馈** → `@mcp-feedback-enhanced`

### 禁止行为
- ❌ 跳过协同工作策略（涉及代码库时）
- ❌ 优先选择基础工具而非@desktop-commander
- ❌ 基于假设编写代码
- ❌ 忘记调用反馈工具

---
**记住：这些规则是自动执行的，AI必须主动遵循，无需用户每次提醒！**