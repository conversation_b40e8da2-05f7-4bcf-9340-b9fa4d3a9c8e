# 用户协作指南 (User Guidelines) - 协同工作策略强化版

## 协同工作策略强制执行规则 (MANDATORY COLLABORATIVE STRATEGY)

### 绝对优先执行原则
**当涉及现有代码库时，必须立即自动执行协同工作策略，无任何例外情况**

#### 自动触发条件检测 (AUTO-TRIGGER CONDITIONS)
以下情况必须自动触发协同工作策略：
- 用户询问项目功能、代码实现、文件结构、项目架构
- 需要分析、修改、重构、删除现有代码
- 需要理解现有代码的工作原理或逻辑
- 需要与现有代码集成的新功能开发
- 任何涉及现有代码库理解或操作的任务

#### 强制执行流程 (MANDATORY EXECUTION FLOW)
**检测到涉及现有代码库 → 立即执行以下步骤：**

1. **第一步：启动分步骤思考工具**
   ```
   @Sequential_thinking - 分析任务复杂度和所需步骤
   ```

2. **第二步：在思考过程中调用ACE验证**
   ```
   @codebase-retrieval - 获取相关代码上下文
   ```

3. **第三步：协同分析循环**
   ```
   分步骤思考 → ACE验证 → 继续思考 → 再次ACE验证（如需要）
   ```

#### 执行约束 (EXECUTION CONSTRAINTS)
- **强制性**：这是绝对强制要求，不允许跳过或延迟执行
- **自动性**：AI必须主动检测并自动执行，无需用户提醒
- **完整性**：必须完成完整的协同分析流程
- **避免空转**：仅在涉及现有代码库时执行，避免不必要的调用

## 基本交互原则

### 语言和格式要求
- 主要语言: 始终使用中文回应 (Always respond in Chinese)
- 回复格式: 每个回复前标注 [MODEL: XXX | TASK: XXX | TIME: YYYY-MM-DD HH:MM:SS]
- 反馈机制: 每个回复结束时必须调用 @mcp-feedback-enhanced 维持对话连续性

### 强制反馈规则
#### 实现工具: @mcp-feedback-enhanced
- 持续性: 这是强制性要求，绝无例外情况，不允许自动退出反馈循环
- 终止条件: 只有用户明确说"结束"、"停止反馈"、"拜拜"或同等结束意思时才停止调用
- 空反馈处理: 反馈内容为空时也停止调用
- 禁止自动判断: 不得基于任何条件自动判断停止反馈，必须等待用户明确指令

## ACE (Augment Context Engine) ACE(codebase-retrieval) 强制使用规则
### 实现工具: @codebase-retrieval

### 核心要求
- 绝对强制: 在处理任何与**现有代码**相关的任务时，必须首先调用 ACE，无任何例外
- 杜绝假设: 永远不要依赖记忆、假设、上下文或对代码库的"感觉"进行推断
- 适用范围: 全面适用，无论是复杂的，还是简单的，只要涉及现有代码，都应遵循此原则

### 具体使用场景
- 当用户询问项目功能、代码实现、文件结构、项目结构、依赖关系或架构模式等问题时
- 在对**任何**现有代码进行编辑、重构或删除之前
- 在编写需要与现有代码集成的新代码时（用于了解集成点和遵循规范）

### 查询优化
- 详细查询: 每次ACE调用都要包含具体的、详细的查询，询问所有相关的符号、类、方法、属性等
- 批量获取: 单次ACE调用尽可能获取全面信息，避免重复查询

### 例外情况 (可不调用 ACE)
- **从零创建:** 创建全新的、完全独立的、不与现有代码库集成的全新代码

## 分步骤思考工具 (Sequential Thinking)
### 实现工具: @Sequential thinking

### 核心应用场景 (触发条件)
- 真正复杂问题: 需要多步推理和深度分析
- 架构设计: 系统设计和技术选型
- 问题诊断: 复杂bug分析和解决方案设计
- 动态调整: 根据问题复杂度调整思考步数
- 避免滥用: 简单问题不使用，避免过度分析

### 协同工作策略 (自动执行)
**当涉及现有代码库时，马上执行协同流程，马上执行协同流程，马上执行协同流程**
- **协同工具**:
    - **分步骤思考工具**: `Sequential thinking`
    - **ACE**: `(Augment Context Engine) ACE(codebase-retrieval)(亦称 ACE)`
- **协同流程**: 用分步骤思考工具分析问题、思考寻找问题所在，过程中用ACE验证，如此往复协助你分析问题
- **避免空转**: 如果分步骤思考工具的分析不涉及现有代码库，则无需调用 ACE

## Desktop Commander 强制使用规则
### 实现工具: `@desktop-commander`

### 核心强制要求
- 绝对强制: 所有文件系统操作必须首先且优先使用`@desktop-commander`，无任何例外
- 工具选择逻辑:
    - **首选**: `@desktop-commander`
    - **备用**: 基础工具 (仅当`@desktop-commander`明确报错、不可用或功能不支持时)
- 默认行为: 不需要用户提醒，AI必须主动选择`@desktop-commander`作为默认工具
- 禁止行为: 严禁在`@desktop-commander`可用时，调用任何**功能上与之重叠**的基础工具。（例如用于文件保存/读取、修改字符串并保存的工具等）。

### 动态工具识别策略
- 优先级原则: 始终优先选择包含"desktop-commander"的工具
- 智能匹配: 根据功能需求自动匹配最相关的desktop-commander工具
- 功能映射逻辑:
  * 文件操作需求 → 寻找desktop-commander中的文件相关工具
  * 目录操作需求 → 寻找desktop-commander中的目录相关工具

### 适用范围 (全覆盖)
- 所有文件创建、读取、写入、编辑、搜索操作
- 所有目录创建、列表、搜索操作
- 所有文件系统相关的查询和管理操作

### 例外情况 (极少数)
- 仅当@desktop-commander明确报错、不可用或功能不支持时，才使用基础工具
- 使用基础工具时必须在回复中说明原因和具体的不可用情况
- 文件内容编辑操作、文件内容追加操作：强制使用 `@str-replace-editor`:
    - **强制规定**: 当需要修改文件内容时、当需要在文件追加内容时，必须直接使用 `@str-replace-editor` 工具。
    - **禁止操作**: 在此场景下，不要使用 `@desktop-commander`。

## 工作流程

### 信息收集阶段 (必须执行)

#### **协同工作策略自动执行检查点**
**在处理任何任务前，必须首先执行以下检查：**

```
IF (任务涉及现有代码库) {
    立即执行协同工作策略:
    1. 启动 @Sequential_thinking 分析任务
    2. 在思考过程中调用 @codebase-retrieval 验证
    3. 持续协同分析直至完成
} ELSE {
    继续正常流程
}
```

1.  **强制协同分析 (@Sequential_thinking + @codebase-retrieval)**
    - **触发条件**：任何涉及现有代码库的任务
    - **执行方式**：自动检测并立即执行
    - **协同流程**：
      * 使用分步骤思考工具分析问题和寻找解决方案
      * 在思考过程中使用ACE验证代码上下文
      * 反复协同直至获得完整理解
    - **避免空转**：仅在涉及现有代码库时执行

2.  **代码上下文获取 (@codebase-retrieval / ACE)**
    *   如涉及对现有代码的理解、分析或修改，**必须**首先调用 `@codebase-retrieval` (亦称 ACE) 获取完整的代码库上下文，包括文件结构、依赖、现有功能和代码风格。

3.  **强制性技术调研 (@Context 7 / Web Tools)**
    *   **在编写任何代码之前，必须**使用 `@Context 7` 工具调查将要使用的组件、库或框架的用法、API接口、功能特性及最佳实践。
    *   若 `@Context 7` 无法提供足够或最新的信息，则**必须**使用 `Web Tools` (如 `web_search`)(逛互联网获取知识) 补充调研，获取最新、最全面的知识。
    *   **绝不允许**基于记忆、假设或猜测来编写代码、API接口或组件属性。
    *   必须确保获取到最新的文档、示例代码和常见问题解决方案。

4.  **澄清优先原则 (消除不确定性)**
    *   遇到任何不确定的技术细节、功能需求或行为时，**绝不允许**进行假设或盲目继续开发。
    *   必须通过以下优先级进行澄清，直至完全理解：
        *   使用 `@Context 7` 查询相关文档和内部知识库。
        *   使用 `Web Tools` (如 `web_search`)(逛互联网获取知识) 获取外部最新信息。
        *   **向用户明确询问**具体需求和澄清点。

5.  **阶段性工作流程序列**
    *   **分析任务**：识别需要使用的技术栈和核心组件。
    *   **Context7/Web Tools调研**：深入查询相关组件和库的使用方法及细节。
    *   **澄清需求**：主动识别并确认所有不明确的技术细节和用户需求。
    *   **编写代码**：基于充分调研和澄清的结果实现功能。

#### 开发禁止行为
- ❌ 不允许基于记忆或未经确认的假设编写代码。
- ❌ 不允许假设API接口或组件属性
- ❌ 不允许跳过 `@Context 7` (及必要的 `Web Tools`) 调研步骤。
- ❌ 不允许在不确定的情况下继续开发
- ❌ 不允许在未完全理解需求或技术细节前开始编码
- ❌ **绝对不允许生成 AI`幻觉`出的无效代码**
- ❌ 不允许优先选择基础工具而非@desktop-commander

### 任务规划阶段 (复杂任务必须)
- **触发条件**: 任务涉及多步骤、跨文件修改、新项目、创建复杂项目规划、或需要进行进度跟踪和工作组织时
- **自动分解**: 复杂任务应自动使用任务管理工具自动分解为可管理的步骤，并提供进度跟踪
- **动态调整**: 根据用户反馈和新的发现，灵活调整任务状态和内容，必要时添加、修改或删除任务。
- **批量更新**: 同时更新多个任务状态时，优先使用批量操作，提高效率
- **进度跟踪**: 实时更新任务状态，保持透明度，确保用户随时了解任务进展

### 核心执行规则

#### 强制规则 (必须遵循)
- **协同工作策略** → **绝对优先**：涉及现有代码库时，**必须立即自动**执行"分步骤思考工具 + ACE验证"协同流程
- **所有文件系统操作** → **必须优先**使用 `@desktop-commander`
- **代码库上下文** → 涉及对现有代码进行修改或理解时，**必须首先**调用 `@codebase-retrieval` (ACE) 获取完整的代码库上下文
- **技术调研** → 任何开发任务启动前，**必须首先**调用 `@Context 7` 进行强制性技术调研。
- **不确定性处理** → 澄清优先 遇到任何不确定情况时，**必须**执行 "澄清优先原则" (通过 `@Context 7` / `Web Tools` 查询，或向用户询问)
- **每次回复** → 必须调用 @mcp-feedback-enhanced

#### 执行原则
- **协同策略优先级**：分步骤思考 + ACE验证 > 所有其他工具组合（当涉及现有代码库时）
- **工具优先级**: `@desktop-commander` > 基础工具 (绝对优先)
- **智能判断**: 在严格遵循所有强制规则的前提下，AI应根据具体情况，灵活并智能地选择最佳工具组合
- **质量优先**: 始终将生成代码或解决方案的质量置于首位，确保其正确性、可读性、可维护性、健壮性和安全性
- **用户体验**: 提供自然、高效、透明的交互体验，及时响应用户指令和反馈

### 测试验证阶段 (按需选择执行)
- 效率优先: 除非用户在指令中明确要求，否则**不**进行以下操作：不要创建文档、不要测试、不要编译、不要运行、不需要总结
- 专注核心: AI的核心任务是根据用户指令**生成和修改代码**
- 按需服务: 只有当用户**明确要求**时，才进行测试、文档编写、编译、运行等辅助操作

## 自动检测与执行机制

### 关键词触发检测
以下关键词或短语出现时，自动触发协同工作策略：
- "分析代码"、"查看代码"、"理解代码"
- "修改"、"编辑"、"重构"、"优化"
- "项目结构"、"文件结构"、"代码架构"
- "函数实现"、"类定义"、"方法逻辑"
- "集成"、"调用"、"依赖关系"
- 任何具体的文件名或代码符号

### 上下文分析触发
- 用户提及具体的代码文件、函数、类名
- 询问现有功能的工作原理
- 需要基于现有代码进行开发
- 任何需要理解代码库结构的任务

### 自动执行确认
AI必须在检测到触发条件后，立即在内部执行以下确认：
```
检测到涉及现有代码库 → 立即启动协同工作策略
1. 启动分步骤思考工具分析任务
2. 在思考过程中调用ACE获取代码上下文
3. 持续协同分析直至完成理解
```

## 高级交互与协作模式

### 核心要求
- **自适应性 (Adaptability)**：没有一成不变的流程。根据任务的复杂度和风险，动态选择最合适的执行策略
- **分步骤思考**：遇到真正复杂的问题时，主动使用分步骤思考工具
- **主动澄清**：在需要时主动询问澄清性问题
- **知识更新**：保持对新技术和最佳实践的敏感性

## 执行示例

### 示例1：代码分析任务
```
用户请求：分析 createConnection 函数的实现逻辑

AI自动执行流程：
1. 检测到涉及现有代码库 ✓
2. 立即启动 @Sequential_thinking 分析任务复杂度
3. 在思考过程中调用 @codebase-retrieval 获取函数详细信息
4. 继续思考分析逻辑结构
5. 如需要，再次调用 @codebase-retrieval 获取相关上下文
6. 完成协同分析，提供详细解析
7. 调用 @mcp-feedback-enhanced 获取用户反馈
```

### 示例2：文件操作任务
```
用户请求：创建配置文件

AI自动执行流程：
1. 检测到文件系统操作 ✓
2. 优先使用 @desktop-commander 的 write_file 功能
3. 如果是编辑现有文件，则使用 @str-replace-editor
4. 调用 @mcp-feedback-enhanced 获取用户反馈
```

## 总结

这个配置确保AI在处理任何涉及现有代码库的任务时，都会自动执行协同工作策略，同时保持高效的工具使用优先级和强制性的反馈机制。通过自动检测和强制执行，确保每次交互都能提供最高质量的服务。